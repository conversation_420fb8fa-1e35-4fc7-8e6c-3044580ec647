"use client"

import styles from "./page.module.scss";
import React, { useEffect, useState } from "react";
import Link from "next/link";
import Image from "next/image";
import { useSession } from "next-auth/react";
import JC_Title from "../components/JC_Title/JC_Title";
import JC_Spinner from "../components/JC_Spinner/JC_Spinner";
import { JC_Get } from "../apiServices/JC_Get";
import { JC_GetRaw } from "../apiServices/JC_GetRaw";
import { JC_Post } from "../apiServices/JC_Post";
import { JC_PostRaw } from "../apiServices/JC_PostRaw";
import { D_UserConfig, UserConfigModel } from "../models/UserConfig";
import { PlaylistModel } from "../models/Playlist";
import { SongModel } from "../models/Song";
import JC_Button from "../components/JC_Button/JC_Button";
import { JC_Utils } from "../Utils";
import { LocalStorageKeyEnum } from "../enums/LocalStorageKey";
import JC_Modal from "../components/JC_Modal/JC_Modal";
import { JC_LocalStorageModel } from "../models/ServiceModels/JC_LocalStorage";


export default function Page_AddLastSong() {


    // - VARIABLES - //

    const session = useSession();
    var userId = session.data?.user.Id;
    var fetchLastSongIntervel:NodeJS.Timeout;

    // - STATE - //

    // Initialised
    const [isInitialised, setIsInitialised] = useState<boolean>(false);
    // Loading
    const [processHasRan, setProcessHasRan] = useState<boolean>(false);
    const [isLoading, setIsLoading] = useState<boolean>(false);
    const [isPlaylistsLoading, setIsPlaylistsLoading] = useState<boolean>(false);
    // Last Song
    const [lastSong, setLastSong] = useState<SongModel>();
    var lastSongVar:SongModel|undefined = lastSong; // Used in "regularlyFetchLastSong()" interval since cannot access state in there
    // Last Song Text Size
    const [lastSongTextSize, setLastSongTextSize] = useState<number>(20); // Default font size from SCSS
    // Playlists
    const [playlists, setPlaylists] = useState<PlaylistModel[]>([]);
    const [playlistsOrig, setPlaylistsOrig] = useState<PlaylistModel[]>([]);
    const [playlistsInEditMode, setPlaylistsInEditMode] = useState<boolean>(false);
    // User Config
    const [userConfig, setUserConfig] = useState<UserConfigModel>();
    // Progress
    const [progressItems, setProgressItems] = useState<PlaylistModel[]>([]);
    // Modal
    const [modalText, setModalText] = useState<string>("");
    // Authentication
    const [isAuthorized, setIsAuthorized] = useState<boolean>(true);
    // Context Menu
    const [contextMenu, setContextMenu] = useState<{
        visible: boolean;
        x: number;
        y: number;
        playlistId: string;
    }>({ visible: false, x: 0, y: 0, playlistId: '' });
    // Search Filter
    const [searchFilter, setSearchFilter] = useState<string>('');


    // - INITIALISE - //

    useEffect(() => {
        // Initialize the page
        const initializePage = async () => {
            try {
                // Check if user is logged in first
                if (!session.data?.user?.Id) {
                    setIsAuthorized(false);
                    setIsInitialised(true);
                    return;
                }

                // Check if user has YT Music tokens without making API calls
                const hasYtMusicTokens = session.data?.user?.YtMusicAuthTokenHash &&
                                       session.data?.user?.YtMusicCookieHash &&
                                       session.data.user.YtMusicAuthTokenHash.length > 0 &&
                                       session.data.user.YtMusicCookieHash.length > 0;

                if (!hasYtMusicTokens) {
                    setIsAuthorized(false);
                    setIsInitialised(true);
                    return;
                }

                // User has tokens, continue loading the page
                setIsAuthorized(true);

                const playlists = await JC_GetRaw<PlaylistModel[]>("ytGetPlaylists", {});

                const userConfig = await JC_Get<UserConfigModel>(UserConfigModel, "userConfig", { userId: userId });
                setUserConfig(userConfig ?? D_UserConfig(userId!));
                userConfig?.SelectedPlaylistIdList?.forEach(pId => playlists.find(p => p.Id == pId) != null ? playlists.find(p => p.Id == pId)!.IsSelected = true : null);
                userConfig?.HiddenPlaylistIdList?.forEach(pId => playlists.find(p => p.Id == pId) != null ? playlists.find(p => p.Id == pId)!.IsHidden = true : null);

                // Replace ImageUrls with cached blob URLs for all playlists
                const cachedPlaylists = await JC_PostRaw("imageCache", playlists);
                // Update the cached URLs back to the original playlists array
                cachedPlaylists.forEach(cached => {
                    const original = playlists.find(p => p.Id === cached.Id);
                    if (original) original.ImageUrl = cached.ImageUrl;
                });

                setPlaylists(playlists);

                // Start the interval to fetch the last song
                await regularlyFetchLastSong();
                fetchLastSongIntervel = setInterval(regularlyFetchLastSong, 1000);
                setIsInitialised(true);
            } catch (error) {
                console.error("Error initializing page:", error);
                setIsAuthorized(false);
                setIsInitialised(true);
            }
        };

        // Start initialization
        initializePage();

        // Cleanup
        return () => {
            if (fetchLastSongIntervel) {
                clearInterval(fetchLastSongIntervel);
            }
        }
    }, [session.data?.user]);


    // - HANDLES - //

    // Adjust text size to fit within container
    async function adjustLastSongTextSize() {
        const textElement = document.querySelector(`.${styles.lastSongText}`) as HTMLElement;
        const containerElement = document.querySelector(`.${styles.lastSong}`) as HTMLElement;

        if (!textElement || !containerElement) return;

        // Reset to default size first
        let currentSize = 20;
        textElement.style.fontSize = `${currentSize}px`;

        // Get container's computed padding
        const containerStyles = window.getComputedStyle(containerElement);
        const paddingTop = parseFloat(containerStyles.paddingTop);
        const paddingBottom = parseFloat(containerStyles.paddingBottom);

        // Shrink text by 0.5px increments until it fits vertically
        while (currentSize > 8) {
            const textRect = textElement.getBoundingClientRect();
            const containerRect = containerElement.getBoundingClientRect();

            // Check if text fits vertically within container's padding + 5px
            const fitsVertically = textRect.top >= (containerRect.top + paddingTop + 3) &&
                                 textRect.bottom <= (containerRect.bottom - paddingBottom - 3);

            if (fitsVertically) {
                break;
            }

            currentSize -= 0.5;
            textElement.style.fontSize = `${currentSize}px`;
        }

        setLastSongTextSize(currentSize);
    }

    // Effect to adjust text size when song changes
    useEffect(() => {
        if (lastSong && isInitialised) {
            // Small delay to ensure DOM is updated
            setTimeout(async () => {
                await adjustLastSongTextSize();
            }, 50);
        }
    }, [lastSong?.Id, isInitialised]);

    // Effect to handle window resize and re-adjust text size
    useEffect(() => {
        async function handleResize() {
            if (lastSong && isInitialised) {
                await adjustLastSongTextSize();
            }
        }

        window.addEventListener('resize', handleResize);
        return () => window.removeEventListener('resize', handleResize);
    }, [lastSong, isInitialised]);

    // Fetch last song every n seconds
    async function regularlyFetchLastSong() {
        try {
            let ytLastSong = await JC_Get<SongModel>(SongModel, "ytGetLastSongFromHistory", {});
            if (!isLoading) {
                if (ytLastSong.Id != lastSongVar?.Id) {
                    setProcessHasRan(false);
                    lastSongVar = ytLastSong;
                    setLastSong(ytLastSong);
                }
            }
            // If we got here, we're authorized
            setIsAuthorized(true);
        } catch (error) {
            // Check if this is an unauthorized error
            if (error instanceof Error && error.message.includes("YouTube Music authentication failed")) {
                // Clear the interval if it exists
                if (fetchLastSongIntervel) {
                    clearInterval(fetchLastSongIntervel);
                }
                // Set unauthorized state
                setIsAuthorized(false);
            }
        }
    }

    // Re-fetch Yt Music playlists
    function reloadPlaylists() {
        setIsPlaylistsLoading(true);
        JC_GetRaw<PlaylistModel[]>("ytGetPlaylists", {}).then(async playlists => {
            userConfig?.SelectedPlaylistIdList?.forEach(pId => playlists.find(p => p.Id == pId) != null ? playlists.find(p => p.Id == pId)!.IsSelected = true : null);
            userConfig?.HiddenPlaylistIdList?.forEach(pId => playlists.find(p => p.Id == pId) != null ? playlists.find(p => p.Id == pId)!.IsHidden = true : null);

            // Replace ImageUrls with cached blob URLs for all playlists
            const cachedPlaylists = await JC_PostRaw("imageCache", playlists);
            // Update the cached URLs back to the original playlists array
            cachedPlaylists.forEach(cached => {
                const original = playlists.find(p => p.Id === cached.Id);
                if (original) original.ImageUrl = cached.ImageUrl;
            });

            setPlaylists(playlists);
            setIsPlaylistsLoading(false);
        });
    }

    // Select Playlist
    function selectPlaylist(playlist:PlaylistModel) {
        playlist.IsSelected = !(playlist.IsSelected??false);
        setPlaylists(JC_Utils.parseStringify(playlists));
        // Clear search filter when selecting a playlist
        setSearchFilter('');
    }

    // Select All Playlists
    function selectAllPlaylists() {
        playlists.filter(p => !p.IsHidden).forEach(p => p.IsSelected = true);
        setPlaylists(JC_Utils.parseStringify(playlists));
        setProcessHasRan(false);
        // Clear search filter when selecting all playlists
        setSearchFilter('');
    }

    // Select No PLaylists
    function selectNoPlaylists() {
        playlists.filter(p => !p.IsHidden).forEach(p => p.IsSelected = false);
        setPlaylists(JC_Utils.parseStringify(playlists));
        setProcessHasRan(false);
        // Clear search filter when deselecting all playlists
        setSearchFilter('');
    }

    // Hide Playlist
    function hidePlaylist(playlist:PlaylistModel) {
        playlist.IsHidden = !(playlist.IsHidden??false);
        setPlaylists(JC_Utils.parseStringify(playlists));
    }

    // Context Menu Functions
    function handleRightClick(event: React.MouseEvent, playlistId: string) {
        // If Shift key is held, allow default browser context menu
        if (event.shiftKey) {
            return;
        }

        event.preventDefault();
        event.stopPropagation();

        // Get the exact position relative to the viewport
        let x = event.clientX;
        let y = event.clientY;

        // Ensure context menu doesn't go off-screen
        const menuWidth = 150; // min-width from CSS
        const menuHeight = 40; // approximate height

        if (x + menuWidth > window.innerWidth) {
            x = window.innerWidth - menuWidth - 10;
        }
        if (y + menuHeight > window.innerHeight) {
            y = window.innerHeight - menuHeight - 10;
        }

        console.log('Right click at:', x, y); // Debug log

        setContextMenu({
            visible: true,
            x: x,
            y: y,
            playlistId: playlistId
        });
    }

    function hideContextMenu() {
        setContextMenu({ visible: false, x: 0, y: 0, playlistId: '' });
    }

    function goToPlaylist(playlistId: string) {
        window.open(`https://music.youtube.com/playlist?list=${playlistId}`, '_blank');
        hideContextMenu();
    }

    // Handle keyboard events for context menu and search filter
    useEffect(() => {
        function handleKeyDown(event: KeyboardEvent) {
            // Check if user has an input field selected (input, textarea, or contenteditable)
            const activeElement = document.activeElement;
            const isInputFieldFocused = activeElement && (
                activeElement.tagName === 'INPUT' ||
                activeElement.tagName === 'TEXTAREA' ||
                activeElement.getAttribute('contenteditable') === 'true'
            );

            // Skip search functionality if an input field is focused
            if (isInputFieldFocused) {
                // Still handle escape key to close context menu even when input is focused
                if (event.key === 'Escape' && contextMenu.visible) {
                    hideContextMenu();
                }
                return;
            }

            // Handle escape key
            if (event.key === 'Escape') {
                if (contextMenu.visible) {
                    hideContextMenu();
                } else if (searchFilter.length > 0) {
                    setSearchFilter('');
                } else {
                    // No search text, unselect the last selected playlist
                    const selectedPlaylists = playlists.filter(p => p.IsSelected && !p.IsHidden);
                    if (selectedPlaylists.length > 0) {
                        const lastSelected = selectedPlaylists[selectedPlaylists.length - 1];
                        selectPlaylist(lastSelected);
                    }
                }
                return;
            }

            // Handle ctrl+backspace to clear search
            if (event.key === 'Backspace' && event.ctrlKey && searchFilter.length > 0) {
                event.preventDefault();
                setSearchFilter('');
                return;
            }

            // Handle backspace for search filter or unselect last playlist
            if (event.key === 'Backspace') {
                if (searchFilter.length > 0) {
                    event.preventDefault();
                    setSearchFilter(prev => prev.slice(0, -1));
                } else {
                    // No search text, unselect the last selected playlist
                    event.preventDefault();
                    const selectedPlaylists = playlists.filter(p => p.IsSelected && !p.IsHidden);
                    if (selectedPlaylists.length > 0) {
                        const lastSelected = selectedPlaylists[selectedPlaylists.length - 1];
                        selectPlaylist(lastSelected);
                    }
                }
                return;
            }

            // Handle enter key to select/deselect first playlist in filtered list
            if (event.key === 'Enter') {
                event.preventDefault();
                const filteredPlaylists = getFilteredPlaylists();
                if (filteredPlaylists.length > 0) {
                    // Find first unselected playlist
                    const firstUnselected = filteredPlaylists.find(p => !p.IsSelected);
                    if (firstUnselected) {
                        // Select the first unselected playlist
                        selectPlaylist(firstUnselected);
                    } else {
                        // All are selected, so deselect the first one
                        const firstSelected = filteredPlaylists.find(p => p.IsSelected);
                        if (firstSelected) {
                            selectPlaylist(firstSelected);
                        }
                    }
                }
                return;
            }

            // Handle alphanumeric and symbol keys for search filter
            if (event.key.length === 1) {
                // Check if it's a number (0-9), letter (a-z, A-Z), or symbol
                const isValidChar = /[0-9a-zA-Z\W]/.test(event.key);
                if (isValidChar) {
                    event.preventDefault();
                    setSearchFilter(prev => prev + event.key);
                }
            }
        }

        document.addEventListener('keydown', handleKeyDown);
        return () => document.removeEventListener('keydown', handleKeyDown);
    }, [contextMenu.visible, searchFilter, playlists]);

    // Save Playlist edits
    function savePlaylistsEdits() {
        // Unselect any that are now hidden
        playlists.filter(p => p.IsHidden).forEach(p => p.IsSelected = false);
        setPlaylists(JC_Utils.parseStringify(playlists));
        // Update playlists in UserConfig
        let selectedPlaylistIds = playlists.filter(p => p.IsSelected).map(p => p.Id);
        let hiddenPlaylistIds = playlists.filter(p => p.IsHidden).map(p => p.Id);
        console.log(userConfig);
        if (userConfig) {
            JC_Post(UserConfigModel, "userConfig", {
                ...userConfig,
                SelectedPlaylistIdList: selectedPlaylistIds,
                SelectedPlaylistIdListJson: JSON.stringify(selectedPlaylistIds),
                HiddenPlaylistIdList: hiddenPlaylistIds,
                HiddenPlaylistIdListJson: JSON.stringify(hiddenPlaylistIds),
            });
        }
        // Turn off Edit mode
        setPlaylistsInEditMode(false);
    }

    // Get filtered playlists based on search filter
    function getFilteredPlaylists() {
        let filteredPlaylists = playlists.filter(p => !p.IsHidden);

        if (searchFilter.length > 0) {
            filteredPlaylists = filteredPlaylists.filter(p =>
                p.Title.toLowerCase().includes(searchFilter.toLowerCase())
            );
        }

        return filteredPlaylists;
    }

    // Run
    async function process() {
        let selectedPlaylistIds = playlists.filter(p => p.IsSelected).map(p => p.Id);
        let hiddenPlaylistIds = playlists.filter(p => p.IsHidden).map(p => p.Id);
        if (session.data == null && selectedPlaylistIds.length > 1) {
            setModalText("You are not signed in so you can only add to 1 playlist at a time.");
        } else {
            setIsLoading(true);
            setProcessHasRan(true);
            setProgressItems([]);

            // Update playlists in UserConfig
            if (userConfig) {
                JC_Post(UserConfigModel, "userConfig", {
                    ...userConfig,
                    SelectedPlaylistIdList: selectedPlaylistIds,
                    SelectedPlaylistIdListJson: JSON.stringify(selectedPlaylistIds),
                    HiddenPlaylistIdList: hiddenPlaylistIds,
                    HiddenPlaylistIdListJson: JSON.stringify(hiddenPlaylistIds),
                });
            }

            // Process
            let selectedPlaylists = playlists.filter(p => p.IsSelected);
            for (const playlist of selectedPlaylists) {
                // Don't await, just shoot them all at once boy
                JC_PostRaw("ytAddToPlaylist", {
                    playlistUrl: `https://music.youtube.com/playlist?list=${playlist.Id}`,
                    song: lastSong
                });
            }
            setProgressItems(selectedPlaylists);

            // Finished
            setIsLoading(false);
            setTimeout(() => document.getElementById("progressContainer")!.scrollTop = document.getElementById("progressContainer")!.scrollHeight+200, 50);
            regularlyFetchLastSong();
        }
    }


    // - MAIN - //

    // If not authorized, show only an error message without any other UI elements
    if (!isAuthorized && isInitialised) {
        return (
            <div className={styles.unauthorizedContainer}>
                <div className={styles.unauthorizedMessage}>
                    <p>YouTube Music authentication failed.</p>
                    <p>Please reset your token to use this feature.</p>
                </div>
            </div>
        );
    }

    return !isInitialised ? <JC_Spinner /> : <div className={styles.mainContainer}>

        {/* Title */}
        <JC_Title title="Add Last Song" />

        {/* Top Buttons */}
        <div className={styles.topButtonsContainer}>
            {/* Run */}
            <JC_Button
                overrideClass={styles.runButtonOverride}
                text="Run"
                isSecondary
                onClick={() => process()}
                isDisabled={playlists.filter(p => p.IsSelected).length == 0 || isLoading || playlistsInEditMode}
            />
            {/* Refresh */}
            <JC_Button
                text="Refresh"
                isSecondary
                onClick={() => { reloadPlaylists(); }}
                isLoading={isPlaylistsLoading}
            />
            {/* History */}
            <JC_Button
                text="History"
                linkToPage="https://music.youtube.com/history"
                linkInNewTab
                linkIsAbsolute
                isSecondary
                isSmall
            />
            {/* Spinner */}
            <JC_Spinner overrideClass={`${styles.spinner} ${!isLoading ? styles.hidden : ""}`} isSmall />
        </div>

        {/* Last Song + Process */}
        <div className={styles.lastSongAndProcessContainer}>

            {/* Last Song */}
            <Link key={lastSong!.Id} href={`https://music.youtube.com/watch?v=${lastSong!.Id}`} target="_blank" className={styles.lastSong}>
                <Image
                    className={styles.lastSongImage}
                    src={lastSong!.ImageUrl}
                    width={200}
                    height={200}
                    alt="SongCover"
                    unoptimized
                />
                <div className={styles.lastSongText} style={{ fontSize: `${lastSongTextSize}px` }}>
                    <div>{lastSong!.Title}</div>
                    <div>{lastSong!.Artist}</div>
                </div>
            </Link>

            {/* Selected Playlists Row or Auto-scrolling All Playlists */}
            {playlists.filter(p => p.IsSelected && !p.IsHidden).length > 0 ? (
                <div className={styles.selectedPlaylistsContainer}>
                    {playlists.filter(p => p.IsSelected && !p.IsHidden).map(p =>
                        <div key={p.Id} className={styles.selectedPlaylistTile}>
                            <div
                                className={styles.selectedPlaylistContent}
                                onClick={() => selectPlaylist(p)}
                                onContextMenu={(e) => handleRightClick(e, p.Id)}
                            >
                                <Image
                                    className={styles.selectedPlaylistImage}
                                    src={p.ImageUrl}
                                    width={100}
                                    height={100}
                                    alt="PlaylistCover"
                                    unoptimized
                                />
                                <div className={styles.selectedPlaylistTitle}>{p.Title}</div>
                            </div>
                        </div>
                    )}
                </div>
            ) : (
                <div className={styles.autoScrollPlaylistsContainer}>
                    <div className={styles.autoScrollPlaylistsTrack}>
                        {/* Duplicate the playlists array to create seamless loop */}
                        {[...playlists.filter(p => !p.IsHidden), ...playlists.filter(p => !p.IsHidden)].map((p, index) =>
                            <div key={`${p.Id}-${index}`} className={styles.autoScrollPlaylistTile}>
                                <div
                                    className={styles.autoScrollPlaylistContent}
                                    onClick={() => selectPlaylist(p)}
                                    onContextMenu={(e) => handleRightClick(e, p.Id)}
                                >
                                    <Image
                                        className={styles.autoScrollPlaylistImage}
                                        src={p.ImageUrl}
                                        width={100}
                                        height={100}
                                        alt="PlaylistCover"
                                        unoptimized
                                    />
                                    <div className={styles.autoScrollPlaylistTitle}>{p.Title}</div>
                                </div>
                            </div>
                        )}
                    </div>
                </div>
            )}

            {/* Process */}
            <div id="progressContainer" className={`${styles.progressContainer} ${!processHasRan && (playlists.filter(p => p.IsSelected).length == 0 || isLoading || playlistsInEditMode) ? styles.waiting : ""}`}>
                {!processHasRan && !(playlists.filter(p => p.IsSelected).length == 0 || isLoading || playlistsInEditMode) && <div className={styles.readyText}>READY</div>}
                {processHasRan &&
                <React.Fragment>
                    <div style={{ marginBottom: "20px", fontSize: "20px" }}>Adding to playlists...</div>
                    {progressItems.map(item =>
                    <React.Fragment key={item.Id}>
                        <Link href={`https://music.youtube.com/playlist?list=${item.Id}`} target="_blank" className={styles.progressItem}>
                            <Image
                                className={styles.itemImage}
                                src={item.ImageUrl}
                                width={100}
                                height={100}
                                alt="PlaylistCover"
                                unoptimized
                            />
                            <div className={styles.progressTitle}>{item.Title}</div>
                        </Link>
                    </React.Fragment>)}
                    {!isLoading && <div style={{ marginTop: "20px", fontSize: "20px" }}>Finished!</div>}
                </React.Fragment>}
            </div>

        </div>

        {/* Playlists */}
        <div className={styles.playlistsAndRunContainer}>
            {/* Playlists */}
            <div className={styles.playlistsContainer}>
                {/* Search Filter Display */}
                {searchFilter.length > 0 && (
                    <div className={styles.searchFilterDisplay}>
                        {searchFilter}
                    </div>
                )}
                {/* Playlist Grid */}
                <div className={styles.playlistGrid}>
                    {!playlistsInEditMode && getFilteredPlaylists().map(p =>
                    <div key={p.Id} className={styles.playlistItem}>
                        {/* Image + Title Container */}
                        <div
                            className={`${styles.imageAndTitleContainer} ${p.IsSelected ? styles.selected : ""} ${p.IsHidden ? styles.hidden : ""}`}
                            onClick={() => selectPlaylist(p)}
                            onContextMenu={(e) => handleRightClick(e, p.Id)}
                        >
                            <Image
                                className={styles.itemImage}
                                src={p.ImageUrl}
                                width={100}
                                height={100}
                                alt="PlaylistCover"
                                unoptimized
                            />
                            <div className={styles.playlistTitleContainer}>
                                <div className={styles.playlistTitle}>{p.Title}</div>
                            </div>
                        </div>
                    </div>)}
                    {playlistsInEditMode && playlists.map(p =>
                    <div key={p.Id} className={styles.playlistItem}>
                        {/* Image + Title Container - Clickable to hide/show */}
                        <div
                            className={`${styles.imageAndTitleContainerEditMode} ${p.IsSelected ? styles.selected : ""} ${p.IsHidden ? styles.hidden : ""}`}
                            onClick={() => hidePlaylist(p)}
                        >
                            <Image
                                className={styles.itemImage}
                                src={p.ImageUrl}
                                width={100}
                                height={100}
                                alt="PlaylistCover"
                                unoptimized
                            />
                            <div className={styles.playlistTitleContainer}>
                                <div className={styles.playlistTitle}>{p.Title}</div>
                            </div>
                        </div>
                    </div>)}
                </div>
            </div>

            {/* Edit Button */}
            {!playlistsInEditMode &&
            <JC_Button
                overrideClass={styles.allNoneButtonOverride}
                text="Edit"
                isSecondary
                isSmall
                onClick={() => { setPlaylistsInEditMode(true); setPlaylistsOrig(JC_Utils.parseStringify(playlists)); setProcessHasRan(false); }}
            />}
            {/* Cancel and Save Buttons */}
            {playlistsInEditMode &&
            <div className={styles.cancelSaveButtonsContainer}>
                <JC_Button
                    overrideClass={styles.allNoneButtonOverride}
                    text="Cancel"
                    isSecondary
                    isSmall
                    onClick={() => { setPlaylistsInEditMode(false); setPlaylists(JC_Utils.parseStringify(playlistsOrig)); setProcessHasRan(false); }}
                />
                <JC_Button
                    overrideClass={styles.allNoneButtonOverride}
                    text="Save"
                    isSecondary
                    isSmall
                    onClick={() => { savePlaylistsEdits(); setProcessHasRan(false); }}
                />
            </div>}
        </div>

        {/* Error Modal */}
        <JC_Modal overrideClass={styles.modalContainer} isOpen={!JC_Utils.stringNullOrEmpty(modalText)} onCancel={() => setModalText("")}>
            {modalText}
            <JC_Button text="Ok" onClick={() => setModalText("")} />
        </JC_Modal>

        {/* Context Menu */}
        {contextMenu.visible && (
            <>
                <div className={styles.contextMenuOverlay} onMouseDown={hideContextMenu} />
                <div
                    className={styles.contextMenu}
                    style={{
                        left: `${contextMenu.x}px`,
                        top: `${contextMenu.y}px`
                    }}
                >
                    <div
                        className={styles.contextMenuItem}
                        onClick={() => goToPlaylist(contextMenu.playlistId)}
                    >
                        Go To Playlist
                    </div>
                </div>
            </>
        )}

    </div>;
}